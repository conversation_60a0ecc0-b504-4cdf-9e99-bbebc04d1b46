import { MedusaService } from "@medusajs/framework/utils"
import Chapter from "./models/chapter"
import Subscription from "./models/subscription"
import UserChapterAccess from "./models/user-chapter-access"
import Translation from "./models/translation"
import ReadingProgress from "./models/reading-progress"

class BookModuleService extends MedusaService({
  Chapter,
  Subscription,
  UserChapterAccess,
  Translation,
  ReadingProgress,
}) {
  // Chapter management
  async listChapters(filters = {}, options = {}) {
    return await this.listAndCount(Chapter, filters, options)
  }

  async getChapter(id: string) {
    return await this.retrieve(Chapter, id)
  }

  async createChapter(data: any) {
    return await this.create(Chapter, data)
  }

  async updateChapter(id: string, data: any) {
    return await this.update(Chapter, id, data)
  }

  async deleteChapter(id: string) {
    return await this.delete(Chapter, id)
  }

  // Subscription management
  async getUserActiveSubscription(userId: string) {
    const subscriptions = await this.list(Subscription, {
      user_id: userId,
      is_active: true,
      expires_at: { $gt: new Date() }
    })
    return subscriptions[0] || null
  }

  async createSubscription(data: any) {
    return await this.create(Subscription, data)
  }

  async updateSubscription(id: string, data: any) {
    return await this.update(Subscription, id, data)
  }

  async cancelSubscription(id: string) {
    return await this.update(Subscription, id, {
      is_active: false,
      cancelled_at: new Date()
    })
  }

  async listSubscriptions(filters = {}, options = {}) {
    return await this.list(Subscription, filters, options)
  }

  // Access control
  async checkUserAccess(userId: string, chapterId: string): Promise<boolean> {
    // Check if chapter is free
    const chapter = await this.getChapter(chapterId)
    if (chapter.is_free) {
      return true
    }

    // Check if user has direct purchase access
    const directAccess = await this.list(UserChapterAccess, {
      user_id: userId,
      chapter_id: chapterId,
      access_type: "purchase"
    })
    if (directAccess.length > 0) {
      return true
    }

    // Check if user has active subscription
    const subscription = await this.getUserActiveSubscription(userId)
    if (subscription) {
      return true
    }

    return false
  }

  async grantChapterAccess(userId: string, chapterId: string, accessType: string, paymentId?: string) {
    const accessData: any = {
      user_id: userId,
      chapter_id: chapterId,
      access_type: accessType,
      purchased_at: new Date()
    }

    if (paymentId) {
      accessData.payment_id = paymentId
    }

    if (accessType === "subscription") {
      const subscription = await this.getUserActiveSubscription(userId)
      if (subscription) {
        accessData.expires_at = subscription.expires_at
      }
    }

    return await this.create(UserChapterAccess, accessData)
  }

  // Reading progress
  async updateReadingProgress(userId: string, chapterId: string, progressData: any) {
    const existing = await this.list(ReadingProgress, {
      user_id: userId,
      chapter_id: chapterId
    })

    if (existing.length > 0) {
      return await this.update(ReadingProgress, existing[0].id, {
        ...progressData,
        updated_at: new Date()
      })
    } else {
      return await this.create(ReadingProgress, {
        user_id: userId,
        chapter_id: chapterId,
        ...progressData
      })
    }
  }

  async getUserReadingProgress(userId: string, chapterId?: string) {
    const filters: any = { user_id: userId }
    if (chapterId) {
      filters.chapter_id = chapterId
    }
    return await this.list(ReadingProgress, filters)
  }

  // Translation management
  async getTranslation(entityId: string, entityType: string, fieldName: string, language: string) {
    const translations = await this.list(Translation, {
      entity_id: entityId,
      entity_type: entityType,
      field_name: fieldName,
      language: language,
      is_approved: true
    })
    return translations[0] || null
  }

  async createTranslation(data: any) {
    return await this.create(Translation, data)
  }

  async updateTranslation(id: string, data: any) {
    return await this.update(Translation, id, data)
  }

  // Analytics and reporting
  async getChapterStats(chapterId: string) {
    const accesses = await this.list(UserChapterAccess, { chapter_id: chapterId })
    const progress = await this.list(ReadingProgress, { chapter_id: chapterId })

    return {
      total_readers: accesses.length,
      completed_readers: progress.filter(p => p.progress_percentage === 100).length,
      average_progress: progress.reduce((sum, p) => sum + p.progress_percentage, 0) / progress.length || 0,
      total_reading_time: progress.reduce((sum, p) => sum + p.time_spent_minutes, 0)
    }
  }

  async getUserStats(userId: string) {
    const progress = await this.list(ReadingProgress, { user_id: userId })
    const accesses = await this.list(UserChapterAccess, { user_id: userId })

    return {
      chapters_accessed: accesses.length,
      chapters_completed: progress.filter(p => p.progress_percentage === 100).length,
      total_reading_time: progress.reduce((sum, p) => sum + p.time_spent_minutes, 0),
      bookmarked_chapters: progress.filter(p => p.bookmarked).length
    }
  }
}

export default BookModuleService
