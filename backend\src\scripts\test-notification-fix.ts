import { ExecArgs } from "@medusajs/framework/types"
import { ContainerRegistrationKeys } from "@medusajs/framework/utils"
import { createOrderFulfillmentWorkflow } from "@medusajs/medusa/core-flows"
import fulfillDigitalOrderWorkflow from "../workflows/fulfill-digital-order"

export default async function testNotificationFix({ container }: ExecArgs) {
  console.log('🧪 Testing Notification Fix')
  console.log('⏰ Timestamp:', new Date().toISOString())
  console.log('=' .repeat(60))

  try {
    const query = container.resolve(ContainerRegistrationKeys.QUERY)

    // Find a recent order with digital products
    const { data: orders } = await query.graph({
      entity: "order",
      fields: [
        "*",
        "items.*",
        "items.variant.*",
        "items.variant.digital_product.*",
        "items.variant.digital_product.medias.*",
        "fulfillments.*",
        "customer.*",
      ],
      filters: {},
      pagination: { take: 5 },
      options: {
        orderBy: { created_at: "DESC" },
      },
    })

    const digitalOrders = orders.filter((order: any) =>
      order.items?.some((item: any) => item.variant?.digital_product)
    )

    if (digitalOrders.length === 0) {
      console.log('❌ No digital orders found to test with')
      return
    }

    const testOrder = digitalOrders[0]
    console.log('📦 Testing with Order:', testOrder.display_id)
    console.log('  - ID:', testOrder.id)
    console.log('  - Customer:', testOrder.customer?.email || testOrder.email)
    console.log('  - Existing Fulfillments:', testOrder.fulfillments?.length || 0)

    // Create a new fulfillment to test the workflow
    console.log('\n🚀 Creating Test Fulfillment...')
    
    const digitalItems = testOrder.items.filter((item: any) => item.variant?.digital_product)
    
    const { result: fulfillmentResult } = await createOrderFulfillmentWorkflow(container).run({
      input: {
        order_id: testOrder.id,
        items: digitalItems.map((item: any) => ({
          id: item.id,
          quantity: item.quantity,
        })),
      },
    })

    console.log('✅ Test Fulfillment Created:')
    console.log('  - Fulfillment ID:', fulfillmentResult?.id)

    // Now test the digital order workflow (this should trigger the notification)
    console.log('\n📧 Testing Digital Order Workflow (with notification)...')
    
    const { result } = await fulfillDigitalOrderWorkflow(container).run({
      input: {
        order_id: testOrder.id,
        fulfillment_id: fulfillmentResult?.id,
      },
    })

    console.log('✅ Digital Order Workflow Completed Successfully!')
    console.log('  - Success:', result?.success)
    console.log('  - Message:', result?.message)
    console.log('  - Digital Product Order ID:', result?.digital_product_order?.id)

    // Check if the digital product order was rolled back
    if (result?.digital_product_order?.id) {
      console.log('✅ Digital product order was NOT rolled back - notification worked!')
    } else {
      console.log('⚠️ Digital product order might have been rolled back')
    }

  } catch (error) {
    console.error('❌ Error in notification test:', error.message)
    console.error('Stack:', error.stack)
    
    if (error.message.includes('NOTIFICATION')) {
      console.error('\n🚨 NOTIFICATION ERROR STILL EXISTS')
      console.error('   The import fix did not resolve the issue')
      console.error('   Check if notification module is properly configured')
    }
  }

  console.log('\n✅ Notification test completed')
  console.log('=' .repeat(60))
}
