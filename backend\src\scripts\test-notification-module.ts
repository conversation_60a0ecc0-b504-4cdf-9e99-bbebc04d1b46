import { ExecArgs } from "@medusajs/framework/types"
import { ModuleRegistrationName } from "@medusajs/framework/utils"

export default async function testNotificationModule({ container }: ExecArgs) {
  console.log('📧 Testing Notification Module Resolution')
  console.log('⏰ Timestamp:', new Date().toISOString())
  console.log('=' .repeat(60))

  try {
    console.log('🔍 Checking ModuleRegistrationName...')
    console.log('  - ModuleRegistrationName object:', ModuleRegistrationName)
    console.log('  - NOTIFICATION key:', ModuleRegistrationName.NOTIFICATION)

    if (!ModuleRegistrationName.NOTIFICATION) {
      console.error('❌ ModuleRegistrationName.NOTIFICATION is undefined!')
      console.error('   This is the source of the error')
      return
    }

    console.log('🔍 Attempting to resolve notification module...')
    const notificationModuleService = container.resolve(ModuleRegistrationName.NOTIFICATION)
    
    console.log('✅ Notification module resolved successfully!')
    console.log('  - Service type:', typeof notificationModuleService)
    console.log('  - Service methods:', Object.getOwnPropertyNames(Object.getPrototypeOf(notificationModuleService)))

    // Test a simple method call
    console.log('🧪 Testing notification service methods...')
    
    if (typeof notificationModuleService.createNotifications === 'function') {
      console.log('✅ createNotifications method exists')
    } else {
      console.log('⚠️ createNotifications method not found')
    }

  } catch (error) {
    console.error('❌ Error testing notification module:', error.message)
    console.error('Stack:', error.stack)
    
    if (error.message.includes('NOTIFICATION')) {
      console.error('\n🚨 ROOT CAUSE IDENTIFIED:')
      console.error('   ModuleRegistrationName.NOTIFICATION is not properly defined')
      console.error('   This suggests the notification module is not configured')
    }
  }

  console.log('\n✅ Notification module test completed')
  console.log('=' .repeat(60))
}
