import type { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { BOOK_MODULE } from "../../../modules/book"

// GET - List all chapters (admin view with full data)
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const bookService = req.scope.resolve(BOOK_MODULE)
    
    // Extract query parameters
    const {
      difficulty_level,
      is_published,
      is_free,
      language,
      limit = 50,
      offset = 0,
      order_by = "order_in_book",
      order_direction = "ASC"
    } = req.query || {}

    // Build filters (admin can see all chapters)
    const filters: any = {}

    if (difficulty_level) {
      filters.difficulty_level = difficulty_level
    }

    if (is_published !== undefined) {
      filters.is_published = is_published === "true" || is_published === true
    }

    if (is_free !== undefined) {
      filters.is_free = is_free === "true" || is_free === true
    }

    if (language) {
      filters.language = language
    }

    // Build options for pagination and sorting
    const options = {
      skip: parseInt(offset as string) || 0,
      take: parseInt(limit as string) || 50,
      order: { [order_by as string]: order_direction }
    }

    console.log('🔧 ADMIN CHAPTERS REQUEST')
    console.log('  - Filters:', JSON.stringify(filters, null, 2))
    console.log('  - Options:', JSON.stringify(options, null, 2))

    // Get chapters using the service
    const [chapters, count] = await bookService.listChapters(filters, options)

    // Calculate summary statistics
    const summary = {
      total_chapters: count,
      published_chapters: chapters.filter((c: any) => c.is_published).length,
      draft_chapters: chapters.filter((c: any) => !c.is_published).length,
      free_chapters: chapters.filter((c: any) => c.is_free).length,
      paid_chapters: chapters.filter((c: any) => !c.is_free && c.price > 0).length,
      total_revenue: chapters
        .filter((c: any) => !c.is_free && c.price > 0)
        .reduce((sum: number, c: any) => sum + (c.price || 0), 0),
      languages: [...new Set(chapters.map((c: any) => c.language))],
      difficulty_levels: [...new Set(chapters.map((c: any) => c.difficulty_level))]
    }

    console.log('✅ ADMIN CHAPTERS RESPONSE')
    console.log('  - Total chapters found:', count)
    console.log('  - Chapters returned:', chapters.length)
    console.log('  - Published:', summary.published_chapters)
    console.log('  - Drafts:', summary.draft_chapters)

    res.json({
      chapters,
      count,
      limit: options.take,
      offset: options.skip,
      summary
    })

  } catch (error) {
    console.error("Error fetching admin chapters:", error)
    res.status(500).json({
      error: "Failed to fetch chapters",
      message: error.message
    })
  }
}

// POST - Create new chapter
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const bookService = req.scope.resolve(BOOK_MODULE)
    
    const chapterData = req.body

    // Validate required fields
    if (!chapterData.title) {
      return res.status(400).json({
        error: "Missing required field",
        message: "title is required"
      })
    }

    // Set defaults
    const newChapterData = {
      difficulty_level: "beginner",
      language: "he",
      is_published: false,
      is_free: false,
      order_in_book: 1,
      ...chapterData,
      created_at: new Date(),
      updated_at: new Date()
    }

    console.log('📝 CREATE CHAPTER REQUEST')
    console.log('  - Title:', newChapterData.title)
    console.log('  - Language:', newChapterData.language)
    console.log('  - Difficulty:', newChapterData.difficulty_level)
    console.log('  - Is Published:', newChapterData.is_published)

    // Create chapter using the service
    const newChapter = await bookService.createChapter(newChapterData)

    console.log('✅ CHAPTER CREATED')
    console.log('  - Chapter ID:', newChapter.id)
    console.log('  - Title:', newChapter.title)

    res.status(201).json({
      chapter: newChapter,
      message: "Chapter created successfully"
    })

  } catch (error) {
    console.error("Error creating chapter:", error)
    res.status(500).json({
      error: "Failed to create chapter",
      message: error.message
    })
  }
}
