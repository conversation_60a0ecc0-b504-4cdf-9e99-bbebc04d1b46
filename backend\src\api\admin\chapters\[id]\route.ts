import type { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { BOOK_MODULE } from "../../../../modules/book"

// GET - Get specific chapter (admin view with full data)
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const bookService = req.scope.resolve(BOOK_MODULE)
    const chapterId = req.params.id

    console.log('🔧 ADMIN CHAPTER REQUEST')
    console.log('  - Chapter ID:', chapterId)

    // Get chapter details
    const chapter = await bookService.getChapter(chapterId)

    if (!chapter) {
      return res.status(404).json({
        error: "Chapter not found",
        message: `Chapter with ID ${chapterId} does not exist`
      })
    }

    // Get chapter statistics
    const stats = await bookService.getChapterStats(chapterId)

    console.log('✅ ADMIN CHAPTER RESPONSE')
    console.log('  - Chapter title:', chapter.title)
    console.log('  - Total readers:', stats.total_readers)
    console.log('  - Completion rate:', Math.round((stats.completed_readers / stats.total_readers) * 100) + '%')

    res.json({
      chapter,
      stats,
      analytics: {
        total_readers: stats.total_readers,
        completed_readers: stats.completed_readers,
        completion_rate: stats.total_readers > 0 ? (stats.completed_readers / stats.total_readers) * 100 : 0,
        average_progress: stats.average_progress,
        total_reading_time: stats.total_reading_time,
        average_reading_time: stats.total_readers > 0 ? stats.total_reading_time / stats.total_readers : 0
      }
    })

  } catch (error) {
    console.error("Error fetching admin chapter:", error)
    res.status(500).json({
      error: "Failed to fetch chapter",
      message: error.message
    })
  }
}

// PUT - Update chapter
export async function PUT(req: MedusaRequest, res: MedusaResponse) {
  try {
    const bookService = req.scope.resolve(BOOK_MODULE)
    const chapterId = req.params.id
    const updateData = req.body

    console.log('📝 UPDATE CHAPTER REQUEST')
    console.log('  - Chapter ID:', chapterId)
    console.log('  - Update fields:', Object.keys(updateData))

    // Check if chapter exists
    const existingChapter = await bookService.getChapter(chapterId)
    if (!existingChapter) {
      return res.status(404).json({
        error: "Chapter not found",
        message: `Chapter with ID ${chapterId} does not exist`
      })
    }

    // Add updated timestamp
    updateData.updated_at = new Date()

    // Update chapter using the service
    const updatedChapter = await bookService.updateChapter(chapterId, updateData)

    console.log('✅ CHAPTER UPDATED')
    console.log('  - Chapter ID:', updatedChapter.id)
    console.log('  - Title:', updatedChapter.title)

    res.json({
      chapter: updatedChapter,
      message: "Chapter updated successfully"
    })

  } catch (error) {
    console.error("Error updating chapter:", error)
    res.status(500).json({
      error: "Failed to update chapter",
      message: error.message
    })
  }
}

// DELETE - Delete chapter
export async function DELETE(req: MedusaRequest, res: MedusaResponse) {
  try {
    const bookService = req.scope.resolve(BOOK_MODULE)
    const chapterId = req.params.id

    console.log('🗑️ DELETE CHAPTER REQUEST')
    console.log('  - Chapter ID:', chapterId)

    // Check if chapter exists
    const existingChapter = await bookService.getChapter(chapterId)
    if (!existingChapter) {
      return res.status(404).json({
        error: "Chapter not found",
        message: `Chapter with ID ${chapterId} does not exist`
      })
    }

    // Delete chapter using the service
    await bookService.deleteChapter(chapterId)

    console.log('✅ CHAPTER DELETED')
    console.log('  - Chapter ID:', chapterId)
    console.log('  - Title:', existingChapter.title)

    res.json({
      message: "Chapter deleted successfully",
      deleted_chapter: {
        id: existingChapter.id,
        title: existingChapter.title
      }
    })

  } catch (error) {
    console.error("Error deleting chapter:", error)
    res.status(500).json({
      error: "Failed to delete chapter",
      message: error.message
    })
  }
}
