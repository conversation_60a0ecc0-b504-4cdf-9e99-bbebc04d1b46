import type { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { BOOK_MODULE } from "../../../../modules/book"

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const bookService = req.scope.resolve(BOOK_MODULE)
    const chapterId = req.params.id

    console.log('📖 PUBLIC CHAPTER REQUEST')
    console.log('  - Chapter ID:', chapterId)

    // Get chapter details
    const chapter = await bookService.getChapter(chapterId)

    if (!chapter) {
      return res.status(404).json({
        error: "Chapter not found",
        message: `Chapter with ID ${chapterId} does not exist`
      })
    }

    // Check if chapter is published (public access only to published chapters)
    if (!chapter.is_published) {
      return res.status(404).json({
        error: "Chapter not found",
        message: "Chapter is not available for public access"
      })
    }

    // Transform chapter for public consumption
    const publicChapter = {
      id: chapter.id,
      title: chapter.title,
      preview_content: chapter.preview_content,
      // Only include full content if chapter is free
      content: chapter.is_free ? chapter.content : undefined,
      difficulty_level: chapter.difficulty_level,
      order_in_book: chapter.order_in_book,
      language: chapter.language,
      price: chapter.price,
      is_free: chapter.is_free,
      audio_url: chapter.audio_url,
      video_url: chapter.video_url,
      reading_time_minutes: chapter.reading_time_minutes,
      tags: chapter.tags || [],
      created_at: chapter.created_at,
      updated_at: chapter.updated_at
    }

    console.log('✅ PUBLIC CHAPTER RESPONSE')
    console.log('  - Chapter title:', chapter.title)
    console.log('  - Is free:', chapter.is_free)
    console.log('  - Language:', chapter.language)
    console.log('  - Full content included:', !!publicChapter.content)

    res.json({
      chapter: publicChapter
    })

  } catch (error) {
    console.error("Error fetching public chapter:", error)
    res.status(500).json({
      error: "Failed to fetch chapter",
      message: error.message
    })
  }
}

// POST endpoint for updating reading progress
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const bookService = req.scope.resolve(BOOK_MODULE)
    const chapterId = req.params.id
    const { user_id, progress_percentage, time_spent_minutes, bookmarked } = req.body || {}

    if (!user_id) {
      return res.status(400).json({
        error: "Missing user_id",
        message: "user_id is required to update reading progress"
      })
    }

    console.log('📊 UPDATE READING PROGRESS')
    console.log('  - Chapter ID:', chapterId)
    console.log('  - User ID:', user_id)
    console.log('  - Progress:', progress_percentage + '%')

    // Update reading progress
    const progressData = {
      progress_percentage: progress_percentage || 0,
      time_spent_minutes: time_spent_minutes || 0,
      bookmarked: bookmarked || false,
      last_read_at: new Date()
    }

    const updatedProgress = await bookService.updateReadingProgress(user_id, chapterId, progressData)

    console.log('✅ READING PROGRESS UPDATED')
    console.log('  - Progress ID:', updatedProgress.id)

    res.json({
      progress: updatedProgress,
      message: "Reading progress updated successfully"
    })

  } catch (error) {
    console.error("Error updating reading progress:", error)
    res.status(500).json({
      error: "Failed to update reading progress",
      message: error.message
    })
  }
}
