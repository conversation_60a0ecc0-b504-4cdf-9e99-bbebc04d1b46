import type { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { BOOK_MODULE } from "../../../modules/book"

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const bookService = req.scope.resolve(BOOK_MODULE)
    
    // Extract query parameters
    const {
      difficulty_level,
      is_free,
      language = "he",
      is_published = true,
      limit = 20,
      offset = 0,
      order_by = "order_in_book"
    } = req.query || {}

    // Build filters
    const filters: any = {
      is_published: is_published === "true" || is_published === true
    }

    if (difficulty_level) {
      filters.difficulty_level = difficulty_level
    }

    if (is_free !== undefined) {
      filters.is_free = is_free === "true" || is_free === true
    }

    if (language) {
      filters.language = language
    }

    // Build options for pagination and sorting
    const options = {
      skip: parseInt(offset as string) || 0,
      take: parseInt(limit as string) || 20,
      order: { [order_by as string]: "ASC" }
    }

    console.log('📚 PUBLIC CHAPTERS REQUEST')
    console.log('  - Filters:', JSON.stringify(filters, null, 2))
    console.log('  - Options:', JSON.stringify(options, null, 2))

    // Get chapters using the service
    const [chapters, count] = await bookService.listChapters(filters, options)

    // Transform chapters for public consumption (remove sensitive data)
    const publicChapters = chapters.map((chapter: any) => ({
      id: chapter.id,
      title: chapter.title,
      preview_content: chapter.preview_content,
      difficulty_level: chapter.difficulty_level,
      order_in_book: chapter.order_in_book,
      language: chapter.language,
      price: chapter.price,
      is_free: chapter.is_free,
      audio_url: chapter.audio_url,
      video_url: chapter.video_url,
      reading_time_minutes: chapter.reading_time_minutes,
      tags: chapter.tags || [],
      created_at: chapter.created_at,
      // Don't expose full content, admin fields, etc.
    }))

    console.log('✅ PUBLIC CHAPTERS RESPONSE')
    console.log('  - Total chapters found:', count)
    console.log('  - Chapters returned:', publicChapters.length)
    console.log('  - First chapter:', publicChapters[0]?.title || 'None')

    res.json({
      chapters: publicChapters,
      count,
      limit: options.take,
      offset: options.skip,
      summary: {
        total_chapters: count,
        free_chapters: publicChapters.filter(c => c.is_free).length,
        paid_chapters: publicChapters.filter(c => !c.is_free).length,
        languages: [...new Set(publicChapters.map(c => c.language))],
        difficulty_levels: [...new Set(publicChapters.map(c => c.difficulty_level))]
      }
    })

  } catch (error) {
    console.error("Error fetching public chapters:", error)
    res.status(500).json({
      error: "Failed to fetch chapters",
      message: error.message
    })
  }
}
