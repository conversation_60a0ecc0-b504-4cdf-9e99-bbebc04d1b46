import { Medusa<PERSON>ontainer } from "@medusajs/framework/types"
import NOTIFICATION_MODULE, { BOOK_MODULE } from "../modules/book"

export default async function checkExpiringSubscriptions(container: MedusaContainer) {
  console.log("Starting subscription expiry check...")
  
  const bookService = container.resolve(BOOK_MODULE)
  const notificationService = container.resolve(NOTIFICATION_MODULE as any)
  
  try {
    // Find subscriptions expiring in the next 7 days
    const sevenDaysFromNow = new Date()
    sevenDaysFromNow.setDate(sevenDaysFromNow.getDate() + 7)
    
    const expiringSubscriptions = await bookService.listSubscriptions({
      is_active: true,
      expires_at: {
        $gte: new Date(),
        $lte: sevenDaysFromNow
      }
    })
    
    console.log(`Found ${expiringSubscriptions.length} subscriptions expiring in the next 7 days`)
    
    for (const subscription of expiringSubscriptions) {
      try {
        // Get user details (in a real app, you'd have a user service)
        const user = {
          id: subscription.user_id,
          email: `user_${subscription.user_id}@example.com`, // Mock email
          first_name: "User"
        }
        
        // Send expiration warning email
        await notificationService.sendSubscriptionExpiration(user, subscription)
        console.log(`Sent expiration warning to user ${subscription.user_id}`)
        
        // Log the notification
        console.log(`Subscription ${subscription.id} expires on ${subscription.expires_at}`)
        
      } catch (error) {
        console.error(`Error processing subscription ${subscription.id}:`, error)
      }
    }
    
    // Find and deactivate expired subscriptions
    const expiredSubscriptions = await bookService.listSubscriptions({
      is_active: true,
      expires_at: {
        $lt: new Date()
      }
    })
    
    console.log(`Found ${expiredSubscriptions.length} expired subscriptions to deactivate`)
    
    for (const subscription of expiredSubscriptions) {
      try {
        await bookService.updateSubscription(subscription.id, {
          is_active: false,
          cancelled_at: new Date()
        })
        
        console.log(`Deactivated expired subscription ${subscription.id}`)

        // You could send a "subscription expired" email here if needed
        // const user = { id: subscription.user_id, email: `user_${subscription.user_id}@example.com`, first_name: "User" }
        // await notificationService.sendSubscriptionExpired(user, subscription)
        
      } catch (error) {
        console.error(`Error deactivating subscription ${subscription.id}:`, error)
      }
    }
    
    // Generate summary report
    const summary = {
      timestamp: new Date().toISOString(),
      expiring_soon: expiringSubscriptions.length,
      expired_and_deactivated: expiredSubscriptions.length,
      notifications_sent: expiringSubscriptions.length,
      errors: 0 // You could track errors here
    }
    
    console.log("Subscription expiry check completed:", summary)
    
    // In a real app, you might want to store this summary in a database
    // or send it to a monitoring service
    
    return summary
    
  } catch (error) {
    console.error("Error in subscription expiry check:", error)
    throw error
  }
}

// Job configuration for scheduling
export const config = {
  name: "subscription-expiry-check",
  schedule: "0 9 * * *", // Run daily at 9 AM
  timezone: "UTC"
}

// Alternative: Manual trigger function for testing
export async function triggerSubscriptionCheck(container: MedusaContainer) {
  console.log("Manually triggering subscription expiry check...")
  return await checkExpiringSubscriptions(container)
}
