import type { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { BOOK_MODULE } from "../../../modules/book"

export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const bookService = req.scope.resolve(BOOK_MODULE)
    
    // Extract query parameters
    const {
      period = "30d",
      metric,
      chapter_id,
      user_id
    } = req.query || {}

    console.log('📊 ADMIN ANALYTICS REQUEST')
    console.log('  - Period:', period)
    console.log('  - Metric:', metric || 'all')
    console.log('  - Chapter ID:', chapter_id || 'all')
    console.log('  - User ID:', user_id || 'all')

    // Get all chapters for overview
    const [chapters, totalChapters] = await bookService.listChapters({}, { take: 1000 })

    // Calculate overview statistics
    const overview = {
      total_chapters: totalChapters,
      published_chapters: chapters.filter((c: any) => c.is_published).length,
      draft_chapters: chapters.filter((c: any) => !c.is_published).length,
      free_chapters: chapters.filter((c: any) => c.is_free).length,
      paid_chapters: chapters.filter((c: any) => !c.is_free && c.price > 0).length,
      total_revenue: chapters
        .filter((c: any) => !c.is_free && c.price > 0)
        .reduce((sum: number, c: any) => sum + (c.price || 0), 0),
      languages: [...new Set(chapters.map((c: any) => c.language))],
      difficulty_levels: [...new Set(chapters.map((c: any) => c.difficulty_level))],
      total_users: 0, // Will be calculated from reading progress
      active_users: 0 // Will be calculated from recent activity
    }

    // Get detailed chapter analytics
    const chapterAnalytics = []
    for (const chapter of chapters.slice(0, 20)) { // Limit to first 20 for performance
      try {
        const stats = await bookService.getChapterStats(chapter.id)
        chapterAnalytics.push({
          chapter_id: chapter.id,
          title: chapter.title,
          difficulty_level: chapter.difficulty_level,
          is_free: chapter.is_free,
          price: chapter.price,
          ...stats,
          completion_rate: stats.total_readers > 0 ? (stats.completed_readers / stats.total_readers) * 100 : 0,
          average_reading_time_per_user: stats.total_readers > 0 ? stats.total_reading_time / stats.total_readers : 0
        })
      } catch (error) {
        console.warn(`Failed to get stats for chapter ${chapter.id}:`, error.message)
      }
    }

    // Calculate user statistics from chapter analytics
    const totalReaders = chapterAnalytics.reduce((sum, c) => sum + c.total_readers, 0)
    const totalCompletedReaders = chapterAnalytics.reduce((sum, c) => sum + c.completed_readers, 0)
    
    overview.total_users = Math.max(totalReaders, 0)
    overview.active_users = Math.max(totalCompletedReaders, 0)

    // Get subscription analytics (if user_id is provided, get user-specific data)
    let subscriptionAnalytics = {}
    try {
      const subscriptions = await bookService.listSubscriptions({})
      subscriptionAnalytics = {
        total_subscriptions: subscriptions.length,
        active_subscriptions: subscriptions.filter((s: any) => s.is_active).length,
        expired_subscriptions: subscriptions.filter((s: any) => !s.is_active).length,
        subscription_revenue: subscriptions.reduce((sum: number, s: any) => sum + (s.price || 0), 0)
      }
    } catch (error) {
      console.warn('Failed to get subscription analytics:', error.message)
      subscriptionAnalytics = {
        total_subscriptions: 0,
        active_subscriptions: 0,
        expired_subscriptions: 0,
        subscription_revenue: 0
      }
    }

    // If specific user analytics requested
    let userAnalytics = null
    if (user_id) {
      try {
        userAnalytics = await bookService.getUserStats(user_id as string)
      } catch (error) {
        console.warn(`Failed to get user stats for ${user_id}:`, error.message)
      }
    }

    // If specific chapter analytics requested
    let specificChapterAnalytics = null
    if (chapter_id) {
      try {
        specificChapterAnalytics = await bookService.getChapterStats(chapter_id as string)
      } catch (error) {
        console.warn(`Failed to get chapter stats for ${chapter_id}:`, error.message)
      }
    }

    // Build response based on requested metric
    const analytics = {
      overview,
      chapters: chapterAnalytics,
      subscriptions: subscriptionAnalytics,
      period,
      generated_at: new Date().toISOString()
    }

    if (userAnalytics) {
      analytics.user = userAnalytics
    }

    if (specificChapterAnalytics) {
      analytics.chapter = specificChapterAnalytics
    }

    // Filter by specific metric if requested
    if (metric) {
      const filteredAnalytics = { [metric]: analytics[metric] || null }
      if (metric === 'revenue') {
        filteredAnalytics.revenue = {
          chapter_revenue: overview.total_revenue,
          subscription_revenue: subscriptionAnalytics.subscription_revenue,
          total_revenue: overview.total_revenue + subscriptionAnalytics.subscription_revenue
        }
      }
      
      console.log('✅ ADMIN ANALYTICS RESPONSE (FILTERED)')
      console.log('  - Metric:', metric)
      console.log('  - Data keys:', Object.keys(filteredAnalytics))
      
      return res.json({ analytics: filteredAnalytics })
    }

    console.log('✅ ADMIN ANALYTICS RESPONSE (FULL)')
    console.log('  - Total chapters:', overview.total_chapters)
    console.log('  - Total users:', overview.total_users)
    console.log('  - Total revenue:', overview.total_revenue + subscriptionAnalytics.subscription_revenue)
    console.log('  - Chapter analytics count:', chapterAnalytics.length)

    res.json({ analytics })

  } catch (error) {
    console.error("Error fetching admin analytics:", error)
    res.status(500).json({
      error: "Failed to fetch analytics",
      message: error.message
    })
  }
}
